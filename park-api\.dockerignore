# ===========================================
# Docker构建忽略文件
# ===========================================

# 版本控制
.git
.gitignore
.gitattributes

# 环境配置文件
.env
.env.local
.env.*.local

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log
*.log.*

# 临时文件
tmp/
temp/
*.tmp
*.temp

# Maven构建产物（除了target目录中的jar文件）
target/classes/
target/test-classes/
target/maven-archiver/
target/maven-status/
target/surefire-reports/
target/generated-sources/
target/generated-test-sources/

# 但保留jar文件
!target/*.jar

# Node.js
node_modules/
dist/
build/

# 文档和说明
README.md
*.md
docs/

# Docker相关
Dockerfile
docker-compose*.yml
.dockerignore

# 测试文件
test/
tests/
*test*
*.test

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 配置文件模板
*.example
*.sample
*.template

# 开发工具配置
.editorconfig
.eslintrc*
.prettierrc*
.stylelintrc*

# 包管理器
package-lock.json
yarn.lock
pnpm-lock.yaml

# Java相关
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# 但保留应用jar文件
!target/lgjy-*.jar

# Spring Boot
spring.log

# 其他
*.pid
*.seed
*.pid.lock
.nyc_output
coverage/
.sass-cache/
connect.lock
typings/
.grunt
bower_components
.lock-wscript
build/releases/
.eslintcache
.stylelintcache

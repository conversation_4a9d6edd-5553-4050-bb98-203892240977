# 网关服务 Dockerfile - 多阶段构建优化版本
FROM eclipse-temurin:8-jdk-alpine AS base

# 设置维护者信息和标签
LABEL maintainer="park-system" \
      service="gateway" \
      version="3.6.9" \
      description="停车场管理系统网关服务"

# 安装必要工具并清理缓存（合并RUN指令减少层数）
RUN apk add --no-cache \
    wget \
    curl \
    tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 创建必要目录并设置权限
RUN mkdir -p /app/logs && \
    chown -R appuser:appgroup /app

# 复制jar包
COPY --chown=appuser:appgroup target/lgjy-gateway.jar app.jar

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 环境变量（从docker-compose传入）
ENV JAVA_OPTS=""
ENV TZ=Asia/Shanghai

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:8080/actuator/health || exit 1

# 启动命令 - 使用exec形式避免shell包装
ENTRYPOINT ["sh", "-c", "exec java $JAVA_OPTS -jar /app/app.jar"]
